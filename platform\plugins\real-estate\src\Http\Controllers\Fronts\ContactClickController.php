<?php

namespace Xmetr\RealEstate\Http\Controllers\Fronts;

use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Base\Http\Responses\BaseHttpResponse;
use Xmetr\RealEstate\Models\Property;
use Xmetr\RealEstate\Models\PropertyContactClick;
use Throwable;

class ContactClickController extends BaseController
{
    public function trackClick(string $propertyId, string $contactType, BaseHttpResponse $response)
    {
        try {
            // Validate contact type
            if (!in_array($contactType, ['whatsapp', 'telegram', 'phone'])) {
                return $response->setError()->setMessage('Invalid contact type');
            }

            // Find the property
            $property = Property::query()->findOrFail($propertyId);

            // Get the agent/author
            $agent = $property->author;
            if (!$agent) {
                return $response->setError()->setMessage('Agent not found');
            }

            // Get the contact information
            $contactInfo = null;
            switch ($contactType) {
                case 'whatsapp':
                    $contactInfo = $agent->getMetaData('whatsapp', true);
                    break;
                case 'telegram':
                    $contactInfo = $agent->getMetaData('telegram', true);
                    break;
                case 'phone':
                    $contactInfo = $agent->phone;
                    break;
            }

            if (!$contactInfo) {
                return $response->setError()->setMessage('Contact information not available');
            }

            // Track the click (only if not already tracked in this session)
            $sessionKey = "contact_click_{$propertyId}_{$contactType}";
            if (!session()->has($sessionKey)) {
                // Increment daily click tracking
                PropertyContactClick::incrementClicks($property->getKey(), $contactType);

                // Increment total clicks on property
                $property::withoutEvents(fn () => $property::withoutTimestamps(fn () => $property->increment($contactType . '_clicks')));

                // Mark as tracked in session to prevent duplicate tracking
                session()->put($sessionKey, time());
            }

            // Generate the appropriate URL
            $redirectUrl = null;
            switch ($contactType) {
                case 'whatsapp':
                    $redirectUrl = "https://wa.me/{$contactInfo}";
                    break;
                case 'telegram':
                    $redirectUrl = "https://t.me/{$contactInfo}";
                    break;
                case 'phone':
                    $redirectUrl = "tel:{$contactInfo}";
                    break;
            }

            return $response->setNextUrl($redirectUrl);

        } catch (Throwable) {
            return $response->setError()->setMessage('An error occurred while processing the request');
        }
    }
}
