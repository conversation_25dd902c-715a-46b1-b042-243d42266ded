<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migration.
     */
    public function up(): void
    {
        Schema::create('re_property_contact_clicks', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('property_id')->constrained('re_properties')->onDelete('cascade');
            $table->enum('contact_type', ['whatsapp', 'telegram', 'phone']);
            $table->date('click_date');
            $table->integer('clicks_count')->unsigned()->default(1);
            $table->timestamps();

            // Ensure one record per property per contact type per day
            $table->unique(['property_id', 'contact_type', 'click_date'], 'property_contact_clicks_unique');

            // Index for faster queries
            $table->index(['property_id', 'contact_type', 'click_date'], 'property_contact_clicks_index');
            $table->index('click_date', 'property_contact_clicks_date_index');
        });
    }

    /**
     * Reverse the migration.
     */
    public function down(): void
    {
        Schema::dropIfExists('re_property_contact_clicks');
    }
};
