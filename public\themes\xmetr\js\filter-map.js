// Filter-map.html js

// Define if city select have changed
let haveCityChanged = false;

// Toggle map
(function () {
    const map = document.querySelector('#x-filter-map')
    document.querySelectorAll('.x-filter-map_trigger').forEach(el => el.addEventListener('click', toggle));

    function toggle() {
        map.classList.toggle('x-filter-map--active')
        document.documentElement.classList.toggle('overflow-hidden')
        document.body.classList.toggle('overflow-hidden')
    }
})();

// Change rent handler
(function () {
    const from = document.querySelector('#filter-rent_from');
    const to = document.querySelector('#filter-rent_to');
    document.querySelectorAll('.x-filter-rent_button').forEach(el => el.addEventListener('click', toggle))

    function toggle(e) {
        const parent = e.target.closest('.x-button-toggle');
        const fromTo = parent.getAttribute('data-changeFromto').split(',')

        from.value = fromTo[0];
        to.value = fromTo[1];
    }
})();

// Change district
(function () {
    const select = document.querySelector('#filter-district');
    document.querySelectorAll('.filter-district_select').forEach(el => el.addEventListener('click', change))

    function change(e) {
        const parent = e.target.closest('.filter-district_select');
        const code = parent.getAttribute('data-selectDistrict');

        select.querySelector(`[value="${code}"]`).selected = 'selected'

        handleMapCityChange(code)
    }
})();

(function () {
    const filtersWrapper = document.querySelector('#x-filtersWrapper');
    const subscribers = document.querySelectorAll('.x-filtersToggled');
    const resetButtons = document.querySelectorAll('.x-filtersReset');
    const citySelects = document.querySelectorAll('.x-select-city');
    const languageElements = document.querySelectorAll('.x-language-speakOn_element');

    if (filtersWrapper) {
        // Handle changing filters count
        filtersWrapper.querySelectorAll('.x-button-toggle').forEach(el => el.addEventListener('click', () => handleFiltersCountChange(filtersWrapper.querySelectorAll('.x-button-toggle--active').length)));
    }

    if (resetButtons.length) {
        // Handle filters reset
        resetButtons.forEach(el => el.addEventListener('click', () => {
            if (filtersWrapper) {
                filtersWrapper.querySelectorAll('.x-button-toggle--active').forEach(el => el.classList.remove('x-button-toggle--active'))
            }

            if (subscribers.length) {
                subscribers.forEach(el => {
                    el.style.display = 'none';
                })
            }

            // Reset select city filter
            if (citySelects.length) {
                citySelects.forEach(el => {
                    const defaultOption = el.querySelector(`[value="city-default"]`);
                    if (defaultOption) {
                        defaultOption.selected = 'selected';
                    }
                })
            }

            if (languageElements.length) {
                languageElements.forEach(el => {
                    el.classList.remove('x-language-speakOn_element--active')
                })
            }
        }))
    }
})();

function handleFiltersCountChange(length) {
    const subscribers = document.querySelectorAll('.x-filtersToggled');

    if (length !== 0) {
        subscribers.forEach(el => {
            el.innerText = length;
            el.style.display = 'block';
        })
    } else {
        subscribers.forEach(el => {
            el.style.display = 'none';
        })
    }
}

// // Handle changing map
// (function () {
//     document.querySelectorAll('.x-select-city').forEach(el => el.addEventListener('change', e => {
//         handleMapCityChange(e.target.value)
//     }))
// })();

// /** @param {string} code  */
// function handleMapCityChange(code) {
//     document.querySelectorAll('.x-select-city').forEach(el => {

//         el.querySelector(`[value="${code}"]`).selected = 'selected'
//     })

//     if (!haveCityChanged) {
//         handleFiltersCountChange(document.querySelector('#x-filtersWrapper').querySelectorAll('.x-button-toggle--active').length + 1)
//         haveCityChanged = true;
//     }

//     // const map = document.querySelector('#x-filters-map');
//     // map.src = `https://maps.google.com/maps?q=${`buenos aires ${city}`}&amp;t=m&amp;z=14&amp;output=embed&amp;iwloc=near`
// }
