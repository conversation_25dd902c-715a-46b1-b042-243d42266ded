<?php
    Theme::set('breadcrumbEnabled', 'no');
    SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
?>


<style>
                .login-options-title{
                display: none;
            }
            .login-options .social-login-basic {
                padding: 0 !important;
            }
            .login-options .social-login-basic .social-login{
                justify-content: center;
                font-size: 14px !important;
                font-weight: 600 !important;
            }
</style>
<div class="wrapper ovh pt-[65px] pb-[40px] max-[576px]:pt-[35px]">
    <div class="container flex flex-col items-center justify-center gap-[10px] h-full">
      <!-- Card -->
      <div class="max-w-[480px] w-full flex flex-col items-center bg-white gap-[20px] rounded-[15px] overflow-hidden pb-[20px]" style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">
        <!-- Header -->
        <div class="w-full flex flex-col items-center gap-[32px] pt-[28px] pb-[20px] px-[30px]" style="background:linear-gradient(180deg, #FDEFE4, #FFFFFF);">
          <img src="<?php echo e(Theme::asset()->url('images/dog.png')); ?>" alt="login-icon" width="80px" height="100px">
          <h3 class="title text-center"><?php echo e(__('Welcome back')); ?></h3>
        </div>

        <?php if(isset($errors) && $errors->has('confirmation')): ?>
            <div class="alert alert-danger">
                <span><?php echo BaseHelper::clean($errors->first('confirmation')); ?></span>
            </div>
            <br>
        <?php endif; ?>

        <div class="px-[20px] flex flex-col gap-[20px]">
          <form action="<?php echo e(route('public.account.login.post')); ?>" method="POST" class="w-full flex flex-col gap-[20px]">
            <?php echo csrf_field(); ?>
            <div class="flex flex-col gap-[10px] w-full">
              <p class="text-black text-[15px] font-bold"><?php echo e(__('Email')); ?> <span class="text-[#FF0000]">*</span></p>

              <input type="email" name="email"  value="<?php echo e(old('email')); ?>" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] <?php if($errors->has('email')): ?> is-invalid <?php endif; ?> " required></input>
                <?php if($errors->has('email')): ?>
                    <div class="invalid-feedback"><?php echo e($errors->first('email')); ?></div>
                <?php endif; ?>
            </div>

            <div class="flex flex-col gap-[10px] w-full">
              <p class="text-black text-[15px] font-bold"><?php echo e(__('Password')); ?> <span class="text-[#FF0000]">*</span></p>

              <input type="password" name="password" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] <?php if($errors->has('password')): ?> is-invalid <?php endif; ?>" required></input>
              <?php if($errors->has('password')): ?>
                <div class="invalid-feedback"><?php echo e($errors->first('password')); ?></div>
              <?php endif; ?>
            </div>

            <button type="submit" class="w-full bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex justify-center gap-[10px] relative grow active:scale-[.95]">
              <p class="text-white text-[15px] font-bold text-center"><?php echo e(__('Log in')); ?></p>
            </button>
          </form>

          <div>
            <?php echo apply_filters(BASE_FILTER_AFTER_LOGIN_OR_REGISTER_FORM, null, \Xmetr\RealEstate\Models\Account::class); ?>

          </div>

          

          <?php if(RealEstateHelper::isRegisterEnabled()): ?>
          <p class="text-[#717171] text-[13px] text-center"><?php echo e(__('By clicking create an account, you accept')); ?> <a href="#" target="_blank" class="underline"><?php echo e(__('the terms and conditions')); ?></a> <?php echo e(__('of use of the site')); ?></p>
          <p class="text-[#717171] text-[15px] text-center"><?php echo e(__('First time on the site?')); ?> <a href="<?php echo e(route('public.account.register')); ?>" class="text-[#5E2DC2] font-bold"><?php echo e(__('Register')); ?></a></p>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/account/auth/login.blade.php ENDPATH**/ ?>