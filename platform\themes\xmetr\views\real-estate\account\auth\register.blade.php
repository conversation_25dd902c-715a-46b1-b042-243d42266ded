@php
    Theme::set('breadcrumbEnabled', 'no');
    SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
@endphp

{{-- {!! $form->renderForm() !!} --}}
<style>
    .login-options-title{
    display: none;
}
.login-options .social-login-basic {
    padding: 0 !important;
}
.login-options .social-login-basic .social-login{
    justify-content: center;
    font-size: 14px !important;
    font-weight: 600 !important;
}
</style>
<div class="wrapper ovh pt-[65px] pb-[40px] max-[576px]:pt-[35px]">
    <div class="container flex flex-col items-center justify-center gap-[10px] h-full">
      <!-- Card -->
      <div class="max-w-[480px] w-full flex flex-col items-center bg-white gap-[20px] rounded-[15px] overflow-hidden pb-[20px]" style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">
        <!-- Header -->
        <div class="w-full flex flex-col items-center gap-[32px] pt-[28px] pb-[20px] px-[30px]" style="background:linear-gradient(180deg, #FDEFE4, #FFFFFF);">
          <img src="{{ Theme::asset()->url('images/singup-dog.png') }}" alt="singup-dog" width="80px" height="100px">
          <h3 class="title text-center">{{ __('Sign Up') }}</h3>
        </div>

        @if (isset($errors) && $errors->has('confirmation'))
            <div class="alert alert-danger">
                <span>{!! BaseHelper::clean($errors->first('confirmation')) !!}</span>
            </div>
            <br>
        @endif

        <div class="px-[20px] flex flex-col gap-[20px]">
          <form action="{{ route('public.account.register.post') }}" method="POST" class="w-full flex flex-col gap-[20px]">
            @csrf
            <div class="flex flex-col gap-[10px] w-full">
              <p class="text-black text-[15px] font-bold">{{ __('Your Name') }} <span class="text-[#FF0000]">*</span></p>

              <input type="test" name="first_name"  value="{{ old('first_name') }}" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] @if ($errors->has('first_name')) is-invalid @endif " required></input>
                @if ($errors->has('first_name'))
                    <div class="invalid-feedback">{{ $errors->first('first_name') }}</div>
                @endif
            </div>
            <div class="flex flex-col gap-[10px] w-full">
              <p class="text-black text-[15px] font-bold">{{ __('Email') }} <span class="text-[#FF0000]">*</span></p>

              <input type="email" name="email"  value="{{ old('email') }}" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] @if ($errors->has('email')) is-invalid @endif " required></input>
                @if ($errors->has('email'))
                    <div class="invalid-feedback">{{ $errors->first('email') }}</div>
                @endif
            </div>

            <div class="flex flex-col gap-[10px] w-full">
              <p class="text-black text-[15px] font-bold">{{ __('Password') }} <span class="text-[#FF0000]">*</span></p>

              <input type="password" name="password" class="border border-[#DDDDDD] rounded-[10px] px-[16px] py-[14px] @if ($errors->has('password')) is-invalid @endif" required></input>
              @if ($errors->has('password'))
                <div class="invalid-feedback">{{ $errors->first('password') }}</div>
              @endif
            </div>

            <button type="submit" class="w-full bg-[#5E2DC2] px-[20px] py-[12px] duration-200 hover:bg-[#5026a5] rounded-[10px] flex justify-center gap-[10px] relative grow active:scale-[.95]">
              <p class="text-white text-[15px] font-bold text-center capitalize">{{ __('Sign up') }}</p>
            </button>
          </form>

          <div>
            {!! apply_filters(BASE_FILTER_AFTER_LOGIN_OR_REGISTER_FORM, null, \Xmetr\RealEstate\Models\Account::class) !!}
          </div>

          {{-- <button class="px-[20px] py-[12px] bg-[#212329] duration-200 hover:bg-[#3e424d] rounded-[10px] flex justify-center gap-[10px] relative grow active:scale-[.95]">
            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M27.5741 13.9221C27.5741 12.9569 27.4875 12.0287 27.3266 11.1377H14.5059V16.4034H21.832C21.5164 18.105 20.5573 19.5467 19.1156 20.5119V23.9275H23.515C26.0891 21.5576 27.5741 18.0678 27.5741 13.9221Z" fill="#4285F4" />
              <path fill-rule="evenodd" clip-rule="evenodd" d="M14.5066 27.2253C18.1821 27.2253 21.2627 26.0066 23.515 23.9275L19.1156 20.5119C17.8967 21.3286 16.3382 21.8111 14.5066 21.8111C10.9611 21.8111 7.96015 19.4165 6.88969 16.199H2.3418V19.7259C4.58171 24.1748 9.18529 27.2253 14.5066 27.2253Z" fill="#34A853" />
              <path fill-rule="evenodd" clip-rule="evenodd" d="M6.88837 16.1994C6.61612 15.3826 6.46143 14.5101 6.46143 13.6129C6.46143 12.7157 6.61612 11.8433 6.88837 11.0265V7.49957H2.34048C1.41853 9.3373 0.892578 11.4163 0.892578 13.6129C0.892578 15.8095 1.41853 17.8886 2.34048 19.7263L6.88837 16.1994Z" fill="#FBBC05" />
              <path fill-rule="evenodd" clip-rule="evenodd" d="M14.5066 5.41416C16.5052 5.41416 18.2996 6.10099 19.7104 7.44989L23.6148 3.5455C21.2573 1.3489 18.1759 0 14.5066 0C9.1853 0 4.58171 3.05049 2.3418 7.49939L6.88969 11.0263C7.96015 7.80877 10.9611 5.41416 14.5066 5.41416Z" fill="#EA4335" />
            </svg>
            <p class="text-white text-[15px] font-bold text-center">Login With Google</p>
          </button> --}}

          @if (RealEstateHelper::isRegisterEnabled())
          <p class="text-[#717171] text-[13px] text-center">{{ __('By clicking create an account, you accept') }} <a href="#" target="_blank" class="underline">{{ __('the terms and conditions') }}</a> {{ __('of use of the site') }}</p>
          <p class="text-[#717171] text-[15px] text-center">{{ __('Have an account?') }} <a href="{{ route('public.account.login') }}" class="text-[#5E2DC2] font-bold">{{ __('Sign in') }}</a></p>
          @endif
        </div>

    </div>
</div>
</div>
</div>
