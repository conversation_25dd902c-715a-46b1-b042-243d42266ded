<div class="agent w-[440px] shrink-0 max-[1280px]:w-full min-[1280px]:pt-[88px]">
    <div class="flex flex-col gap-[20px] items-center">
        @if (! RealEstateHelper::hideAgentInfoInPropertyDetailPage() && ($account = $property->author))
        <div class="px-[20px] py-[30px] bg-[#F7F7F7] rounded-[10px] flex flex-col gap-[30px] max-[1280px]:w-full" id="apartmentsContacts" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);">
        <div class="flex items-center gap-[20px]">
          {{-- <img src="./images/avatars/1.png" alt="Avatar" class="object-cover shrink-0 rounded-[32px]" width="100px" height="100px"> --}}
          {{ RvMedia::image($account->avatar->url ?: $account->avatar_url, $account->name, 'thumb', false, ['class'=> 'object-cover shrink-0 rounded-[32px]', 'width'=> '100px', 'height'=>'100px']) }}

          <div class="w-full flex flex-col gap-[15px]">
            <div class="flex flex-col gap-[5px]">
              <p class="text-black text-[15px] font-bold text-ellipsis line-clamp-1">{{ $account->name }}</p>

              <div class="flex items-center gap-[10px]">
                <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19.5562 8.74032L18.1961 7.16071C17.9361 6.86078 17.7261 6.30092 17.7261 5.90102V4.20145C17.7261 3.14171 16.8561 2.27193 15.796 2.27193H14.0959C13.7059 2.27193 13.1358 2.06198 12.8358 1.80205L11.2557 0.442389C10.5657 -0.147463 9.43561 -0.147463 8.73556 0.442389L7.16543 1.81205C6.86541 2.06198 6.29538 2.27193 5.90535 2.27193H4.17525C3.11519 2.27193 2.24514 3.14171 2.24514 4.20145V5.91102C2.24514 6.30092 2.03512 6.86078 1.78511 7.16071L0.435026 8.75032C-0.145009 9.44015 -0.145009 10.5599 0.435026 11.2497L1.78511 12.8393C2.03512 13.1392 2.24514 13.6991 2.24514 14.089V15.7986C2.24514 16.8583 3.11519 17.7281 4.17525 17.7281H5.90535C6.29538 17.7281 6.86541 17.938 7.16543 18.198L8.74557 19.5576C9.43561 20.1475 10.5657 20.1475 11.2657 19.5576L12.8458 18.198C13.1458 17.938 13.7059 17.7281 14.1059 17.7281H15.806C16.8661 17.7281 17.7361 16.8583 17.7361 15.7986V14.099C17.7361 13.7091 17.9461 13.1392 18.2061 12.8393L19.5662 11.2597C20.1463 10.5699 20.1462 9.43015 19.5562 8.74032ZM14.1559 8.11048L9.3256 12.9393C9.18559 13.0792 8.99558 13.1592 8.79557 13.1592C8.59556 13.1592 8.40554 13.0792 8.26554 12.9393L5.84535 10.5199C5.55533 10.2299 5.55533 9.75007 5.84535 9.46014C6.13537 9.17021 6.6154 9.17021 6.90542 9.46014L8.79557 11.3497L13.0958 7.05073C13.3858 6.76081 13.8659 6.76081 14.1559 7.05073C14.4459 7.34066 14.4459 7.82054 14.1559 8.11048Z" fill="#0071FF" />
                </svg>

                <p class="text-[15px] text-[#717171] max-[430px]:text-[13px]">{{ $account->with_xmetr }}</p>
              </div>

              {{-- <div class="w-full flex items-start gap-[8px]">
                <div class="w-fit flex flex-wrap items-center gap-[10px]">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z" fill="#F0F0F0" />
                    <path d="M2.48062 4.69416C1.53802 5.92055 0.82725 7.33406 0.413391 8.86964H6.65611L2.48062 4.69416Z" fill="#0052B4" />
                    <path d="M23.5866 8.86964C23.1728 7.33411 22.4619 5.92059 21.5194 4.6942L17.344 8.86964H23.5866Z" fill="#0052B4" />
                    <path d="M0.413391 15.1305C0.827297 16.666 1.53806 18.0795 2.48062 19.3059L6.65597 15.1305H0.413391Z" fill="#0052B4" />
                    <path d="M19.3058 2.48067C18.0795 1.53806 16.666 0.827297 15.1304 0.413391V6.65606L19.3058 2.48067Z" fill="#0052B4" />
                    <path d="M4.69416 21.5193C5.92055 22.4619 7.33406 23.1727 8.86959 23.5866V17.344L4.69416 21.5193Z" fill="#0052B4" />
                    <path d="M8.86955 0.413391C7.33402 0.827297 5.9205 1.53806 4.69416 2.48062L8.86955 6.65602V0.413391Z" fill="#0052B4" />
                    <path d="M15.1305 23.5866C16.666 23.1727 18.0795 22.4619 19.3058 21.5194L15.1305 17.344V23.5866Z" fill="#0052B4" />
                    <path d="M17.344 15.1305L21.5194 19.3059C22.4619 18.0796 23.1728 16.666 23.5866 15.1305H17.344Z" fill="#0052B4" />
                    <path d="M23.8984 10.4348H13.5653V0.101578C13.0529 0.034875 12.5305 0 12 0C11.4694 0 10.9471 0.034875 10.4348 0.101578V10.4347H0.101578C0.034875 10.9471 0 11.4695 0 12C0 12.5306 0.034875 13.0529 0.101578 13.5652H10.4347V23.8984C10.9471 23.9651 11.4694 24 12 24C12.5305 24 13.0529 23.9652 13.5652 23.8984V13.5653H23.8984C23.9651 13.0529 24 12.5306 24 12C24 11.4695 23.9651 10.9471 23.8984 10.4348Z" fill="#D80027" />
                    <path d="M15.1305 15.1305L20.4853 20.4853C20.7315 20.2391 20.9665 19.9817 21.1906 19.7149L16.6062 15.1305H15.1305V15.1305Z" fill="#D80027" />
                    <path d="M8.86955 15.1305H8.86945L3.51469 20.4853C3.76088 20.7315 4.01827 20.9665 4.28508 21.1906L8.86955 16.6061V15.1305Z" fill="#D80027" />
                    <path d="M8.86955 8.86964V8.86955L3.51473 3.51469C3.26845 3.76088 3.03352 4.01827 2.80936 4.28508L7.39387 8.86959L8.86955 8.86964Z" fill="#D80027" />
                    <path d="M15.1305 8.86964L20.4853 3.51473C20.2391 3.26845 19.9817 3.03352 19.7149 2.80941L15.1305 7.39392V8.86964Z" fill="#D80027" />
                  </svg>

                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0 12C0 14.36 0.681135 16.56 1.84307 18.4H22.1569C23.3189 16.56 24 14.36 24 12C24 9.64 23.3189 7.44 22.1569 5.6H1.84307C0.681135 7.44 0 9.64 0 12Z" fill="#FFCE31" />
                    <path d="M22.197 5.6C20.0735 2.24 16.3072 0 12.02 0C7.73289 0 3.96661 2.24 1.84307 5.6H22.197Z" fill="#ED4C5C" />
                    <path d="M1.84307 18.4C3.96661 21.76 7.73289 24 12.02 24C16.3072 24 20.0735 21.76 22.197 18.4H1.84307Z" fill="#ED4C5C" />
                    <path d="M2.88481 10.68H4.16694V11.4H2.88481V10.68Z" fill="#C8B100" />
                    <path d="M2.88481 15.96H4.20701V16.64H2.88481V15.96Z" fill="#C8B100" />
                    <path d="M2.76461 14.84C2.64441 14.92 2.56427 15 2.56427 15.04C2.56427 15.08 2.60434 15.12 2.68447 15.16C2.76461 15.2 2.84474 15.28 2.80467 15.36C2.88481 15.28 2.92487 15.2 2.92487 15.12C2.92487 15 2.84474 14.88 2.76461 14.84Z" fill="#ED4C5C" />
                    <path d="M3.08514 11.4H4.00668V15.96H3.08514V11.4Z" fill="white" />
                    <path d="M4.96828 13.08C4.76795 13 4.40735 12.92 4.00668 12.92C3.88648 12.92 3.72621 12.92 3.56594 12.96C3.00501 13.04 2.56427 13.28 2.60434 13.44L2.40401 13C2.36394 12.8 2.84474 12.56 3.44574 12.44C3.64608 12.4 3.84641 12.4 4.00668 12.4C4.40735 12.4 4.76795 12.44 4.96828 12.52V13.08Z" fill="#ED4C5C" />
                    <path d="M3.08514 13.68C2.84474 13.68 2.64441 13.6 2.64441 13.48C2.64441 13.4 2.72454 13.28 2.88481 13.2H3.12521L3.08514 13.68Z" fill="#ED4C5C" />
                    <path d="M4.00668 13.32C4.16694 13.36 4.28715 13.4 4.36728 13.44C4.40735 13.48 4.24708 13.64 4.00668 13.76V13.32Z" fill="#ED4C5C" />
                    <path d="M2.48414 14.56C2.44407 14.48 2.72454 14.32 3.08514 14.2C3.24541 14.16 3.36561 14.08 3.56594 14C4.04674 13.8 4.44741 13.52 4.36728 13.44L4.44741 13.92C4.48748 14 4.16694 14.24 3.68614 14.48C3.52588 14.56 3.24541 14.68 3.08514 14.72C2.80467 14.8 2.56427 14.96 2.56427 15L2.48414 14.56Z" fill="#ED4C5C" />
                    <path d="M11.4992 10.68H12.7813V11.4H11.4992V10.68Z" fill="#C8B100" />
                    <path d="M11.4591 15.96H12.7813V16.64H11.4591V15.96Z" fill="#C8B100" />
                    <path d="M12.9015 14.84C13.0217 14.92 13.1018 15 13.1018 15.04C13.1018 15.08 13.0618 15.12 12.9816 15.16C12.9015 15.24 12.8214 15.36 12.8614 15.4C12.7813 15.32 12.7412 15.24 12.7412 15.16C12.7412 15 12.8214 14.88 12.9015 14.84Z" fill="#ED4C5C" />
                    <path d="M11.6594 11.4H12.581V15.96H11.6594V11.4Z" fill="white" />
                    <path d="M10.6978 13.08C10.8982 13 11.2588 12.92 11.6594 12.92C11.7796 12.92 11.9399 12.92 12.1002 12.96C12.6611 13.04 13.1018 13.28 13.0618 13.44L13.2621 12.96C13.3022 12.76 12.8214 12.52 12.2204 12.4H11.6594C11.2588 12.4 10.8982 12.44 10.6978 12.52V13.08Z" fill="#ED4C5C" />
                    <path d="M12.581 13.68C12.8214 13.68 13.0217 13.6 13.0217 13.48C13.0217 13.4 12.9416 13.28 12.7813 13.2H12.5409L12.581 13.68Z" fill="#ED4C5C" />
                    <path d="M11.6594 13.32C11.4992 13.36 11.379 13.4 11.2988 13.44C11.2588 13.48 11.419 13.64 11.6594 13.76V13.32Z" fill="#ED4C5C" />
                    <path d="M13.182 14.56C13.222 14.48 12.9416 14.32 12.581 14.2C12.4207 14.16 12.3005 14.08 12.1002 14C11.6194 13.8 11.2187 13.52 11.2988 13.44L11.2187 13.92C11.1786 14 11.4992 14.24 11.98 14.48C12.1402 14.56 12.4207 14.68 12.581 14.72C12.8614 14.8 13.1018 15 13.0618 15.04L13.182 14.56Z" fill="#ED4C5C" />
                    <path d="M7.81302 8.12C8.57429 8.12 10.1369 8.28 10.6978 8.84C10.0968 10.28 9.13522 9.68 7.81302 9.68C6.53088 9.68 5.52922 10.28 4.92821 8.84C5.48915 8.28 7.01169 8.12 7.81302 8.12Z" fill="#ED4C5C" />
                    <path d="M9.77629 9.72C9.29549 9.44 8.57429 9.4 7.81302 9.4C7.05175 9.4 6.33055 9.48 5.84975 9.72L6.01002 10.4C6.45075 10.52 7.09182 10.6 7.81302 10.6C8.53422 10.6 9.13523 10.52 9.61603 10.4L9.77629 9.72Z" fill="#C8B100" />
                    <path d="M10.4574 8C10.2972 7.88 9.97663 7.76 9.69616 7.76C9.57596 7.76 9.45576 7.76 9.33556 7.8C9.33556 7.8 9.09516 7.48 8.53422 7.48C8.33389 7.48 8.17362 7.52 8.01336 7.6V7.56C7.97329 7.48 7.89315 7.4 7.81302 7.4C7.73289 7.4 7.61269 7.52 7.61269 7.6V7.64C7.45242 7.56 7.29215 7.52 7.09182 7.52C6.53088 7.52 6.29048 7.88 6.29048 7.84C6.17028 7.8 6.05008 7.8 5.92988 7.8C4.08681 7.8 5.00835 9.04 5.00835 9.04L5.20868 8.8C4.76795 8.24 5.16861 7.92 5.96995 7.92C6.09015 7.92 6.17028 7.92 6.25042 7.96C5.96995 8.36 6.49082 8.72 6.49082 8.72L6.61102 8.52C6.33055 8.32 6.29048 7.64 7.09182 7.64C7.29215 7.64 7.45242 7.68 7.61269 7.8C7.61269 7.84 7.57262 8.4 7.53255 8.48L7.85309 8.76L8.17362 8.48C8.13356 8.36 8.09349 7.84 8.09349 7.8C8.21369 7.72 8.41402 7.64 8.61436 7.64C9.45576 7.64 9.45576 8.32 9.09516 8.52L9.21536 8.72C9.21536 8.72 9.65609 8.36 9.45576 7.96C9.53589 7.96 9.65609 7.92 9.73623 7.92C10.6978 7.92 10.7379 8.64 10.4975 8.8L10.6578 9.04C10.5776 9.04 11.0184 8.48 10.4574 8Z" fill="#C8B100" />
                    <path d="M7.57262 7.24C7.57262 7.12 7.69282 7 7.81302 7C7.97329 7 8.05342 7.12 8.05342 7.24C8.05342 7.36 7.93322 7.48 7.81302 7.48C7.69282 7.48 7.57262 7.36 7.57262 7.24Z" fill="#005BBF" />
                    <path d="M7.73289 6.56V6.68H7.61269V6.8H7.73289V7.2H7.57262V7.32H8.05342L8.09349 7.24L8.05342 7.2H7.89316V6.8H8.01336V6.68H7.89316V6.56H7.73289Z" fill="#C8B100" />
                    <path d="M7.81302 10.52C7.17195 10.52 6.61102 10.44 6.17028 10.32C6.61102 10.2 7.17195 10.12 7.81302 10.12C8.45409 10.12 9.01503 10.2 9.45576 10.32C9.05509 10.44 8.45409 10.52 7.81302 10.52Z" fill="#ED4C5C" />
                    <path d="M7.85309 17.44C7.09182 17.44 6.37062 17.24 5.72955 16.96C5.24875 16.72 4.96828 16.28 4.96828 15.76V13.84H10.7379V15.76C10.7379 16.28 10.4174 16.76 9.97663 16.96C9.33556 17.28 8.61436 17.44 7.85309 17.44Z" fill="white" />
                    <path d="M7.81302 10.64H10.6978V13.84H7.81302V10.64Z" fill="white" />
                    <path d="M7.85309 15.76C7.85309 16.52 7.21202 17.12 6.41068 17.12C5.60935 17.12 4.96828 16.52 4.96828 15.76V13.84H7.85309V15.76Z" fill="#ED4C5C" />
                    <path d="M5.56928 16.88C5.64942 16.92 5.76962 17 5.92988 17.04V13.76H5.60935L5.56928 16.88Z" fill="#C8B100" />
                    <path d="M4.92821 15.72C4.92821 16.12 5.08848 16.44 5.24875 16.6V13.76H4.92821V15.72Z" fill="#C8B100" />
                    <path d="M6.21035 17.12H6.53088V13.76H6.21035V17.12Z" fill="#C7B500" />
                    <path d="M6.85142 17.04C6.97162 17 7.13189 16.92 7.21202 16.88V13.76H6.89149L6.85142 17.04Z" fill="#C8B100" />
                    <path d="M4.92821 10.64H7.81302V13.84H4.92821V10.64Z" fill="#ED4C5C" />
                    <path d="M7.53255 16.6C7.69282 16.48 7.81302 16.2 7.85309 15.88V13.8H7.53255V16.6Z" fill="#C8B100" />
                    <path d="M10.7379 13.84V15.76C10.7379 16.52 10.0968 17.12 9.29549 17.12C8.49416 17.12 7.85309 16.52 7.85309 15.76V13.84H10.7379Z" fill="#ED4C5C" />
                    <path d="M9.69616 11.2C9.81636 11.44 9.81636 12.04 9.45576 11.92C9.53589 11.96 9.57596 12.24 9.69616 12.4C9.89649 12.64 10.1369 12.44 10.0968 12.16C10.0167 11.72 10.0568 11.44 10.1369 11C10.1369 11.04 10.3372 11.04 10.4174 10.96C10.3773 11.08 10.3372 11.24 10.4174 11.24C10.3372 11.36 10.1369 11.56 10.0968 11.68C10.0568 11.96 10.4975 12.48 10.0167 12.6C9.69616 12.68 9.89649 12.92 10.0167 13.04C10.0167 13.04 9.85643 13.56 9.93656 13.52C9.61603 13.64 9.69616 13.36 9.69616 13.36C9.85643 12.88 9.41569 12.84 9.45576 12.76C9.05509 12.72 9.49583 13.12 9.13523 13.12C9.05509 13.12 8.89483 13.2 8.89483 13.2C8.45409 13.16 8.69449 12.76 8.85476 12.8C8.97496 12.84 9.09516 13.04 9.09516 12.76C9.09516 12.76 8.89483 12.44 9.41569 12.44C9.21536 12.44 9.09516 12.28 9.01503 12.08C8.93489 12.12 8.81469 12.32 8.37396 12.36C8.37396 12.36 8.25376 11.92 8.37396 12C8.53422 12.08 8.61436 12.08 8.77462 11.92C8.69449 11.8 8.21369 11.64 8.29382 11.36C8.29382 11.28 8.53422 11.16 8.53422 11.16C8.49416 11.36 8.61436 11.56 8.85476 11.56C9.17529 11.6 9.05509 11.48 9.09516 11.4C9.13523 11.32 9.37563 11.44 9.29549 11.24C9.29549 11.2 9.01503 11.16 9.09516 11.04C9.25543 10.84 9.49583 11 9.69616 11.2Z" fill="#ED4C5C" />
                    <path d="M7.85309 17.04L7.77295 16.84L7.85309 16.6L7.93322 16.84L7.85309 17.04Z" fill="#ED4C5C" />
                    <path d="M5.80968 11.32V11.52H5.88982V11.68H5.68948V12.08H5.80968V12.96H5.56928V13.4H7.21202V12.96H7.01169V12.08H7.09182V11.68H6.89149V11.52H7.01169V11.32H6.61102V11.52H6.69115V11.68H6.49082V11.2H6.61102V11H6.17028V11.2H6.29048V11.68H6.09015V11.52H6.17028V11.32H5.80968Z" fill="#C8B100" />
                    <path d="M10.3372 16.24V14.24H8.25376V16.24L9.21536 16.68H9.33556L10.3372 16.24ZM9.21536 14.4V15.08L8.53422 14.4H9.21536ZM8.37396 14.44L9.17529 15.24L8.37396 16.04V14.44ZM8.45409 16.2L9.21536 15.44V16.56L8.45409 16.2ZM9.33556 16.52V15.4L10.0968 16.16L9.33556 16.52ZM10.177 16.04L9.37563 15.24L10.177 14.44V16.04ZM9.33556 14.4H10.0167L9.33556 15.08V14.4Z" fill="#C8B100" />
                    <path d="M6.89149 13.8C6.89149 13.2 7.29215 12.76 7.81302 12.76C8.33389 12.76 8.73456 13.24 8.73456 13.8C8.73456 14.36 8.33389 14.84 7.81302 14.84C7.29215 14.84 6.89149 14.4 6.89149 13.8Z" fill="#ED4C5C" />
                    <path d="M7.17195 13.8C7.17195 13.36 7.45242 13.04 7.81302 13.04C8.17362 13.04 8.45409 13.4 8.45409 13.8C8.45409 14.24 8.17362 14.56 7.81302 14.56C7.49249 14.6 7.17195 14.24 7.17195 13.8Z" fill="#005BBF" />
                    <path d="M7.53255 13.28L7.37229 13.72L7.49249 13.76L7.41235 13.92H7.65276L7.57262 13.76L7.69282 13.72L7.53255 13.28Z" fill="#C8B100" />
                    <path d="M8.13356 13.28L7.97329 13.72L8.09349 13.76L8.01336 13.92H8.25376L8.21369 13.76L8.33389 13.72L8.13356 13.28Z" fill="#C8B100" />
                    <path d="M7.85309 13.8L7.65275 14.24L7.77295 14.28L7.73289 14.44H7.93322L7.89316 14.28L8.01336 14.24L7.85309 13.8Z" fill="#C8B100" />
                  </svg>
                </div>

                <p class="shrink-0 text-[13px] text-[#717171]">{{ __('Speak') }}</p>
              </div> --}}
            </div>

            <a href="{{ $account->url }}" class="text-black text-[15px] underline">{{ $account->properties_count }} {{ __('listing') }}</a>
          </div>
        </div>


        <div class="flex items-center gap-[10px]">
            @if ($whatsapp = $account->getMetaData('whatsapp', true))
          <a href="@if (auth('account')->check()) https://wa.me/{{ $whatsapp }} @else #modalSignin @endif"  @if (auth('account')->check()) target="_blank"  @else  role="button"  data-bs-toggle="modal"  @endif class="h-[50px] rounded-[10px] bg-[#7DC678] flex justify-center items-center gap-[8px] px-[33px] w-full max-[500px]:px-[8px]">
            <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4.40471 6.15476C4.5051 5.36085 5.53688 4.28185 6.35572 4.40158L6.35441 4.40027C7.1513 4.5517 7.78212 5.92327 8.13783 6.54C8.38986 6.98747 8.2262 7.44084 7.99112 7.63214C7.67399 7.88775 7.17807 8.24043 7.28212 8.59455C7.4671 9.22407 9.66331 11.4203 10.7435 12.0446C11.1523 12.2809 11.4488 11.6576 11.702 11.3383C11.886 11.0927 12.3397 10.9459 12.7861 11.1884C13.4529 11.5766 14.081 12.0279 14.6619 12.5359C14.952 12.7784 15.02 13.1367 14.819 13.5155C14.465 14.1825 13.4411 15.0621 12.6978 14.8833C11.3995 14.5712 6.14938 12.5359 4.46298 6.63958C4.36814 6.36064 4.39202 6.25518 4.40471 6.15476Z" fill="white" />
              <path fill-rule="evenodd" clip-rule="evenodd" d="M9.66331 19.3266C8.5884 19.3266 7.99367 19.2113 7.02786 18.8874L5.17816 19.8122C4.00994 20.3964 2.63545 19.5469 2.63545 18.2407V16.2519C0.74368 14.4879 0 12.454 0 9.66331C0 4.32641 4.32641 0 9.66331 0C15.0002 0 19.3266 4.32641 19.3266 9.66331C19.3266 15.0002 15.0002 19.3266 9.66331 19.3266ZM4.39241 15.4879L3.83365 14.9669C2.36388 13.5965 1.75697 12.0643 1.75697 9.66331C1.75697 5.29676 5.29676 1.75697 9.66331 1.75697C14.0299 1.75697 17.5697 5.29676 17.5697 9.66331C17.5697 14.0299 14.0299 17.5697 9.66331 17.5697C8.79739 17.5697 8.39127 17.4915 7.58653 17.2216L6.89475 16.9896L4.39241 18.2407V15.4879Z" fill="white" />
            </svg>

            <p class="text-white text-[15px] font-bold">{{ __('WhatsApp') }}</p>
          </a>
          @endif
          @if ($telegram = $account->getMetaData('telegram', true))
          <a href=" @if (auth('account')->check()) https://t.me/{{ $telegram }} @else #modalSignin @endif" @if (auth('account')->check()) target="_blank"  @else  role="button"  data-bs-toggle="modal"  @endif class="h-[50px] rounded-[10px] bg-[#4DA0D9] flex justify-center items-center gap-[8px] px-[33px] w-full max-[500px]:px-[8px]">
            <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M19.9615 2.17749C20.2493 0.774406 18.8712 -0.391584 17.5352 0.124619L1.15816 6.45211C-0.333844 7.02856 -0.400324 9.11491 1.05194 9.78514L4.6184 11.4312L6.31501 17.3693C6.40314 17.6778 6.6479 17.9166 6.95843 17.9971C7.26895 18.0775 7.59888 17.9877 7.82571 17.7608L10.4392 15.1473L14.1001 17.8931C15.1628 18.6901 16.6934 18.1096 16.9603 16.8083L19.9615 2.17749ZM1.81073 8.14112L18.1878 1.81364L15.1865 16.4445L10.8974 13.2276C10.537 12.9573 10.0327 12.9932 9.71406 13.3117L8.59469 14.4311L8.93103 12.5814L15.5212 5.99131C15.8419 5.67059 15.8758 5.16203 15.6005 4.80159C15.3251 4.44113 14.8257 4.34003 14.4318 4.56507L5.33067 9.76568L1.81073 8.14112ZM6.44037 11.217L6.98935 13.1386L7.20013 11.9793C7.23307 11.7981 7.32048 11.6312 7.4507 11.5011L9.46048 9.49136L6.44037 11.217Z" fill="white" />
            </svg>

            <p class="text-white text-[15px] font-bold">{{ __('Telegram') }}</p>
          </a>
          @endif

          @if ($account->phone)

          <a href=" @if (auth('account')->check()) tel:{{ $account->phone }} @else #modalSignin @endif"  @if (auth('account')->check()) target="_blank"  @else  role="button"  data-bs-toggle="modal"  @endif class="w-[50px] h-[50px] shrink-0 rounded-[10px] bg-[#212329] hover:bg-[#3e424d] flex justify-center items-center gap-[8px] px-[8px]">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15.6202 8.75169C15.1902 8.75169 14.8501 8.40168 14.8501 7.98164C14.8501 7.61164 14.4801 6.84163 13.8601 6.17162C13.2501 5.52162 12.5801 5.14161 12.0201 5.14161C11.5901 5.14161 11.2501 4.79161 11.2501 4.3716C11.2501 3.9516 11.6001 3.6016 12.0201 3.6016C13.0201 3.6016 14.0701 4.1416 14.9901 5.11161C15.8502 6.02162 16.4002 7.15163 16.4002 7.97164C16.4002 8.40168 16.0502 8.75169 15.6202 8.75169Z" fill="white" />
              <path d="M19.23 8.75009C18.8 8.75009 18.46 8.40008 18.46 7.98008C18.46 4.43004 15.57 1.55002 12.0299 1.55002C11.5999 1.55002 11.2599 1.20001 11.2599 0.780008C11.2599 0.360004 11.5999 0 12.0199 0C16.42 0 20 3.58003 20 7.98008C20 8.40008 19.65 8.75009 19.23 8.75009Z" fill="white" />
              <path d="M9.05009 12.9501L7.20007 14.8001C6.81007 15.1902 6.19006 15.1902 5.79006 14.8101C5.68006 14.7001 5.57006 14.6001 5.46005 14.4901C4.43004 13.4501 3.50004 12.3601 2.67003 11.2201C1.85002 10.0801 1.19001 8.94009 0.710007 7.81008C0.240002 6.67007 0 5.58006 0 4.54005C0 3.86004 0.120001 3.21003 0.360004 2.61003C0.600006 2.00002 0.98001 1.44001 1.51002 0.940009C2.15002 0.310003 2.85003 0 3.59004 0C3.87004 0 4.15004 0.0600007 4.40004 0.180002C4.66005 0.300003 4.89005 0.480005 5.07005 0.740007L7.39007 4.01004C7.57008 4.26004 7.70008 4.49005 7.79008 4.71005C7.88008 4.92005 7.93008 5.13005 7.93008 5.32005C7.93008 5.56006 7.86008 5.80006 7.72008 6.03006C7.59008 6.26006 7.40007 6.50006 7.16007 6.74007L6.40006 7.53008C6.29006 7.64008 6.24006 7.77008 6.24006 7.93008C6.24006 8.01008 6.25006 8.08008 6.27006 8.16008C6.30006 8.24008 6.33006 8.30008 6.35006 8.36008C6.53007 8.69009 6.84007 9.12009 7.28007 9.6401C7.73008 10.1601 8.21008 10.6901 8.73009 11.2201C8.83009 11.3201 8.94009 11.4201 9.04009 11.5201C9.44009 11.9101 9.45009 12.5501 9.05009 12.9501Z" fill="white" />
              <path d="M19.9698 16.3293C19.9698 16.6093 19.9198 16.8993 19.8198 17.1793C19.7898 17.2593 19.7598 17.3393 19.7198 17.4193C19.5498 17.7793 19.3298 18.1193 19.0398 18.4393C18.5498 18.9793 18.0098 19.3693 17.3998 19.6193C17.3898 19.6193 17.3798 19.6293 17.3698 19.6293C16.7798 19.8693 16.1398 19.9993 15.4498 19.9993C14.4297 19.9993 13.3397 19.7593 12.1897 19.2693C11.0397 18.7793 9.8897 18.1193 8.74969 17.2893C8.35968 16.9993 7.96969 16.7093 7.59969 16.3993L10.8697 13.1292C11.1497 13.3392 11.3997 13.4992 11.6097 13.6092C11.6597 13.6292 11.7197 13.6592 11.7897 13.6892C11.8697 13.7192 11.9497 13.7292 12.0397 13.7292C12.2097 13.7292 12.3397 13.6692 12.4497 13.5592L13.2097 12.8092C13.4597 12.5592 13.6997 12.3692 13.9297 12.2492C14.1597 12.1092 14.3897 12.0392 14.6397 12.0392C14.8297 12.0392 15.0297 12.0792 15.2498 12.1692C15.4698 12.2592 15.6998 12.3892 15.9498 12.5592L19.2598 14.9092C19.5198 15.0893 19.6998 15.2993 19.8098 15.5493C19.9098 15.7993 19.9698 16.0493 19.9698 16.3293Z" fill="white" />
            </svg>
          </a>
          @endif
        </div>


        </div>
        @endif

      <div class="flex items-center gap-[4px]">
        <p class="text-[15px]">😡</p>
        <a href="#modalSendReport" data-bs-toggle="modal" class="text-[#DC6F5A] text-[15px] underline underline-offset-2">{{ __('Report') }}</a>
      </div>
    </div>
  </div>
