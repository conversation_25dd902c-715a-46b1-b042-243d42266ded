<input
    name="language"
    type="hidden"
    value="<?php echo e($currentLanguage?->lang_code); ?>"
>
<div id="list-others-language">
    <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(!$currentLanguage || $language->lang_code === $currentLanguage->lang_code) continue; ?>
        <a
            class="gap-2 d-flex align-items-center text-decoration-none"
            href="<?php echo e(Route::has($route['edit']) ? Request::url() . ($language->lang_code != Language::getDefaultLocaleCode() ? '?' . Language::refLangKey() . '=' . $language->lang_code : null) : '#'); ?>"
            target="_blank"
        >
            <?php echo language_flag($language->lang_flag, $language->lang_name); ?>

            <span><?php echo e($language->lang_name); ?> <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-external-link'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?></span>
        </a>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<?php $__env->startPush('header'); ?>
    <meta
        name="<?php echo e(Language::refFromKey()); ?>"
        content="<?php echo e(!empty($args[0]) && $args[0]->id ? $args[0]->id : 0); ?>"
    >
    <meta
        name="<?php echo e(Language::refLangKey()); ?>"
        content="<?php echo e($currentLanguage?->lang_code); ?>"
    >
<?php $__env->stopPush(); ?>
<?php /**PATH D:\laragon\www\xmetr\platform/plugins/language-advanced/resources/views/language-box.blade.php ENDPATH**/ ?>