@php

@endphp


<div class="flex flex-col grow m-0   bg-white rounded-t-[10px] rounded-b-[10px] overflow-hidden">
    <a href="{{ $property->url }}" class="relative flex flex-col justify-between p-[20px] w-full aspect-[16/10] list-thumb">

        <div class="swiper apartment-swiper absolute top-0 left-0 w-full h-full">
        <div class="swiper-wrapper">
        @foreach($property->images as $image)
            @if($loop->iteration > 3) @break @endif
          <div class="swiper-slide"><img src="{{ RvMedia::getImageUrl($image) }}" alt="{{ $property->name }}" class="w-full h-full object-cover"></div>
          @endforeach
        </div>

        <div class="swiper-pagination apartment-swiper_pagination w-full flex justify-center items-center !absolute !left-0 !bottom-[8px]"></div>
        <div class="absolute bottom-0 left-0 w-full h-[95px] z-[2] pointer-events-none touch-none" style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
      </div>

      <div class="flex justify-between gap-[16px] z-[2]">
        <!-- Badges -->
        <div class="flex gap-[5px] flex-wrap items-start">
            @if($property->is_featured)
            <div class="rounded-[5px] bg-[#E45E45] px-[10px] py-[5px]">
              <p class="text-white text-[13px] font-bold">⚡ Featured</p>
            </div>
            @endif
            @if($property->time_ago_text)
            <div class="px-[8px] py-[5px] bg-white rounded-[5px] w-fit">
              <p class="text-[13px] text-black font-bold">{{ $property->time_ago_text }}</p>
            </div>
            @endif
        </div>

         <!-- Favorite (Add .x-favorite--active) to activate favorite button -->
            @if (RealEstateHelper::isEnabledWishlist())
            <button type="button" class="x-favorite p-[15px] bg-[#5E2DC2]/[.15] rounded-[30px] w-[50px] h-[50px] relative"
                    data-type="property"
                    data-bb-toggle="add-to-wishlist"
                    data-id="{{ $property->getKey() }}"
                    data-add-message="{{ __('Added ":name" to wishlist successfully!', ['name' => $property->name]) }}"
                    data-remove-message="{{ __('Removed ":name" from wishlist successfully!', ['name' => $property->name]) }}"
            >

                <svg class="x-favorite_icon x-favorite-notFilled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="white"></path>
                </svg>
                <svg class="x-favorite_icon x-favorite-filled" width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M2 3.5L5.5 0.5L10 4.5L14 0.5L18.5 3L19.5 5L18 9.5L15 13L14 14.5L10 17.5L6.5 14L3.5 12L1 8.5L2 3.5Z" fill="#FF0000"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.43762 3.55751C1.79286 4.66283 1.58065 6.19655 1.97681 7.7812C2.11937 8.35136 2.53381 9.08448 3.19064 9.9283C3.83472 10.7557 4.65681 11.6207 5.52752 12.4505C7.25399 14.0962 9.11202 15.5477 10 16.219C10.888 15.5477 12.746 14.0962 14.4724 12.4505C15.3432 11.6207 16.1653 10.7557 16.8093 9.9283C17.4662 9.08448 17.8806 8.35136 18.0232 7.7812C18.4193 6.19655 18.2072 4.66284 17.5624 3.55751C16.933 2.47865 15.8822 1.77704 14.4426 1.77704C13.1901 1.77704 12.3074 2.70312 11.6832 3.95143C11.3833 4.55124 11.1795 5.16002 11.0505 5.62445C10.9864 5.85497 10.942 6.04579 10.9139 6.17673C10.8145 6.64071 10.2793 6.66389 10 6.66389C9.72074 6.66389 9.18461 6.63639 9.08607 6.17673C9.058 6.0458 9.01357 5.85497 8.94951 5.62445C8.8205 5.16002 8.61667 4.55125 8.3168 3.95144C7.69259 2.70312 6.80983 1.77704 5.5574 1.77704C4.11783 1.77704 3.06695 2.47865 2.43762 3.55751ZM10 3.34981C10.0303 3.2856 10.0616 3.22119 10.0938 3.15672C10.8024 1.73947 12.141 0 14.4426 0C16.5571 0 18.1718 1.07542 19.0974 2.66212C20.0075 4.22234 20.2396 6.2427 19.7471 8.21213C19.5139 9.14525 18.9166 10.1142 18.2116 11.0198C17.4939 11.9419 16.6041 12.8738 15.6985 13.7369C13.8866 15.4639 11.9509 16.9726 11.0493 17.6533C10.4268 18.1235 9.57325 18.1235 8.95066 17.6533C8.04911 16.9726 6.11348 15.4639 4.30148 13.7369C3.39594 12.8738 2.50612 11.9419 1.78837 11.0198C1.08338 10.1142 0.486107 9.14525 0.252836 8.21213C-0.239533 6.2427 -0.00747901 4.22235 0.902653 2.66212C1.82822 1.07542 3.44289 0 5.5574 0C7.85901 0 9.19758 1.73947 9.90618 3.15671C9.93843 3.22119 9.96971 3.2856 10 3.34981Z" fill="#FF0000"></path>
                </svg>
            </button>
            @endif
      </div>

      {{-- <div class="px-[10px] py-[5px] rounded-[5px] z-[2] bg-white w-fit" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);">
        <p class="text-black text-[17px]"><b class="font-bold">{{ format_price($property->price, $property->currency) }}</b> / {{ strtolower($property->period->label()) }}</p>
      </div> --}}

      <div class="absolute bottom-0 left-0 w-full h-[95px]" style="background: linear-gradient(180deg, rgba(33, 35, 41, 0),rgba(33, 35, 41, .8));"></div>
    </a>

    <div class="px-[20px] pt-[20px] pb-[30px] bg-white border border-[#D6D6D7] rounded-b-[10px] flex flex-col gap-[15px]">

        <div class="px-[10px] py-[5px] rounded-[5px] z-[2] bg-white w-fit" style="box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.25);">
        <p class="text-black text-[17px]"><b class="font-bold">{{ format_price($property->price, $property->currency) }}</b> / {{ strtolower($property->period->label()) }}</p>
      </div>

        <div class="flex items-center gap-[5px] flex-wrap">
            @if ($property->city->name)
            <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit">
                <p class="text-[13px] text-[#717171]">
                    @if($property->categories->isNotEmpty())
                    {{ implode(', ', $property->categories->map(function($category) { return $category->name; })->toArray()) }}
                    ,
                    @endif
                    {{ $property->country->name }}, {{ $property->city->name }}</p>
            </div>
            @endif

            @if ($property->features->isNotEmpty())
                @foreach ($property->features as $feature)
                    @if($feature->icon)
                        @if ($featured = $feature->getMetaData('featured', true))
                            <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit" data-bs-toggle="tooltip" title="{{ $feature->name }}">
                                <p class="text-[13px]">{{ $feature->icon }}</p>
                            </div>
                        @endif
                    @endif
                @endforeach
            @endif
            @if ($deposit = $property->getMetaData('deposit', true))
            <div class="px-[8px] py-[5px] bg-[#F7F7F7] rounded-[5px] w-fit">
                <p class="text-[13px] text-[#717171]">{{ __('Deposit') }} : {{ format_price((float)$deposit, $property->currency) }}</p>
            </div>
            @endif




        </div>



        <div class="flex items-center gap-[20px] flex-wrap">
            @if($property->number_bedroom)
                <p class="text-[15px] text-black">🛌 {{ number_format($property->number_bedroom) }} {{ __('bedroom') }}</p>
            @endif
            @if($property->number_bathroom)
                <p class="text-[15px] text-black">🛀🏻 {{ number_format($property->number_bathroom) }} {{ __('bathroom') }}</p>
            @endif
            @if($property->square)
                <p class="text-[15px] text-black">📐 {{ $property->square_text }}</p>
            @endif

        </div>

      {{-- <p class="text-ellipsis line-clamp-2 text-[15px] text-[#717171]">
        {!! Str::limit(BaseHelper::clean($property->description)) !!}
      </p> --}}

      @if (! \Xmetr\RealEstate\Facades\RealEstateHelper::isDisabledPublicProfile() && ($author = $property->author) && $property->author->name)
      {{-- <span class="w-full h-[1px] bg-[#D6D6D7] block agent-grid-area"></span> --}}

      <div class="flex items-center gap-[10px] agent-grid-area">
        {{-- <img src="{{ Theme::asset()->url('images/avatars/1.png') }}" alt="Avatar" class="" width="60px" height="60px"> --}}

        {{ RvMedia::image($author->avatar_url, $author->name, 'thumb', false , ['class' => 'object-cover shrink-0 rounded-[32px] max-[430px]:w-[40px] max-[430px]:h-[40px]', 'width'=>'60px', 'height'=> '60px']) }}

        <div class="w-full flex flex-col gap-[5px]">
          <p class="text-black text-[15px] font-bold text-ellipsis line-clamp-1">{{ $author->name }}</p>

          <div class="flex items-center gap-[10px]">
            {{-- <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19.5562 8.74032L18.1961 7.16071C17.9361 6.86078 17.7261 6.30092 17.7261 5.90102V4.20145C17.7261 3.14171 16.8561 2.27193 15.796 2.27193H14.0959C13.7059 2.27193 13.1358 2.06198 12.8358 1.80205L11.2557 0.442389C10.5657 -0.147463 9.43561 -0.147463 8.73556 0.442389L7.16543 1.81205C6.86541 2.06198 6.29538 2.27193 5.90535 2.27193H4.17525C3.11519 2.27193 2.24514 3.14171 2.24514 4.20145V5.91102C2.24514 6.30092 2.03512 6.86078 1.78511 7.16071L0.435026 8.75032C-0.145009 9.44015 -0.145009 10.5599 0.435026 11.2497L1.78511 12.8393C2.03512 13.1392 2.24514 13.6991 2.24514 14.089V15.7986C2.24514 16.8583 3.11519 17.7281 4.17525 17.7281H5.90535C6.29538 17.7281 6.86541 17.938 7.16543 18.198L8.74557 19.5576C9.43561 20.1475 10.5657 20.1475 11.2657 19.5576L12.8458 18.198C13.1458 17.938 13.7059 17.7281 14.1059 17.7281H15.806C16.8661 17.7281 17.7361 16.8583 17.7361 15.7986V14.099C17.7361 13.7091 17.9461 13.1392 18.2061 12.8393L19.5662 11.2597C20.1463 10.5699 20.1462 9.43015 19.5562 8.74032ZM14.1559 8.11048L9.3256 12.9393C9.18559 13.0792 8.99558 13.1592 8.79557 13.1592C8.59556 13.1592 8.40554 13.0792 8.26554 12.9393L5.84535 10.5199C5.55533 10.2299 5.55533 9.75007 5.84535 9.46014C6.13537 9.17021 6.6154 9.17021 6.90542 9.46014L8.79557 11.3497L13.0958 7.05073C13.3858 6.76081 13.8659 6.76081 14.1559 7.05073C14.4459 7.34066 14.4459 7.82054 14.1559 8.11048Z" fill="#0071FF" />
            </svg> --}}

            {{-- <p class="text-[15px] text-[#717171] max-[430px]:text-[13px]">{{ __('Agent') }}</p> --}}
          </div>
        </div>

        <div class="flex items-center gap-[10px]">
            @if ($whatsapp = $author->getMetaData('whatsapp', true))
            <a href="@if (auth('account')->check()) {{ route('public.contact.click', ['property' => $property->id, 'type' => 'whatsapp']) }} @else #modalSignin @endif"  @if (auth('account')->check()) target="_blank"  @else  role="button"  data-bs-toggle="modal"  @endif class="w-[50px] h-[50px] rounded-[10px] bg-[#7DC678] flex justify-center items-center max-[430px]:w-[40px] max-[430px]:h-[40px]">
                <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4.40471 6.15476C4.5051 5.36085 5.53688 4.28185 6.35572 4.40158L6.35441 4.40027C7.1513 4.5517 7.78212 5.92327 8.13783 6.54C8.38986 6.98747 8.2262 7.44084 7.99112 7.63214C7.67399 7.88775 7.17807 8.24043 7.28212 8.59455C7.4671 9.22407 9.66331 11.4203 10.7435 12.0446C11.1523 12.2809 11.4488 11.6576 11.702 11.3383C11.886 11.0927 12.3397 10.9459 12.7861 11.1884C13.4529 11.5766 14.081 12.0279 14.6619 12.5359C14.952 12.7784 15.02 13.1367 14.819 13.5155C14.465 14.1825 13.4411 15.0621 12.6978 14.8833C11.3995 14.5712 6.14938 12.5359 4.46298 6.63958C4.36814 6.36064 4.39202 6.25518 4.40471 6.15476Z" fill="white" />
                <path fill-rule="evenodd" clip-rule="evenodd" d="M9.66331 19.3266C8.5884 19.3266 7.99367 19.2113 7.02786 18.8874L5.17816 19.8122C4.00994 20.3964 2.63545 19.5469 2.63545 18.2407V16.2519C0.74368 14.4879 0 12.454 0 9.66331C0 4.32641 4.32641 0 9.66331 0C15.0002 0 19.3266 4.32641 19.3266 9.66331C19.3266 15.0002 15.0002 19.3266 9.66331 19.3266ZM4.39241 15.4879L3.83365 14.9669C2.36388 13.5965 1.75697 12.0643 1.75697 9.66331C1.75697 5.29676 5.29676 1.75697 9.66331 1.75697C14.0299 1.75697 17.5697 5.29676 17.5697 9.66331C17.5697 14.0299 14.0299 17.5697 9.66331 17.5697C8.79739 17.5697 8.39127 17.4915 7.58653 17.2216L6.89475 16.9896L4.39241 18.2407V15.4879Z" fill="white" />
                </svg>
            </a>
            @endif

            @if ($telegram = $author->getMetaData('telegram', true))
          <a href="@if (auth('account')->check()) {{ route('public.contact.click', ['property' => $property->id, 'type' => 'telegram']) }} @else #modalSignin @endif" @if (auth('account')->check()) target="_blank"  @else  role="button"  data-bs-toggle="modal"  @endif class="w-[50px] h-[50px] rounded-[10px] bg-[#4DA0D9] flex justify-center items-center max-[430px]:w-[40px] max-[430px]:h-[40px]">
            <svg width="20" height="20" class="max-[430px]:w-[16px] max-[430px]:h-[16px]" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M19.9615 2.17749C20.2493 0.774406 18.8712 -0.391584 17.5352 0.124619L1.15816 6.45211C-0.333844 7.02856 -0.400324 9.11491 1.05194 9.78514L4.6184 11.4312L6.31501 17.3693C6.40314 17.6778 6.6479 17.9166 6.95843 17.9971C7.26895 18.0775 7.59888 17.9877 7.82571 17.7608L10.4392 15.1473L14.1001 17.8931C15.1628 18.6901 16.6934 18.1096 16.9603 16.8083L19.9615 2.17749ZM1.81073 8.14112L18.1878 1.81364L15.1865 16.4445L10.8974 13.2276C10.537 12.9573 10.0327 12.9932 9.71406 13.3117L8.59469 14.4311L8.93103 12.5814L15.5212 5.99131C15.8419 5.67059 15.8758 5.16203 15.6005 4.80159C15.3251 4.44113 14.8257 4.34003 14.4318 4.56507L5.33067 9.76568L1.81073 8.14112ZM6.44037 11.217L6.98935 13.1386L7.20013 11.9793C7.23307 11.7981 7.32048 11.6312 7.4507 11.5011L9.46048 9.49136L6.44037 11.217Z" fill="white" />
            </svg>
          </a>
          @endif
        </div>
      </div>

      @endif

    </div>
</div>
