<?php

namespace Xmetr\RealEstate\Models;

use Xmetr\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class PropertyDailyView extends BaseModel
{
    protected $table = 're_property_daily_views';

    protected $fillable = [
        'property_id',
        'view_date',
        'views_count',
    ];

    protected $casts = [
        'view_date' => 'date',
        'views_count' => 'integer',
    ];

    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class, 'property_id');
    }

    /**
     * Increment views for a property on a specific date
     */
    public static function incrementViews(int $propertyId, ?Carbon $date = null): void
    {
        $date = $date ?: Carbon::today();

        $record = static::firstOrCreate(
            [
                'property_id' => $propertyId,
                'view_date' => $date->format('Y-m-d'),
            ],
            [
                'views_count' => 0,
            ]
        );

        $record->increment('views_count');
    }

    /**
     * Get views count for a property on a specific date
     */
    public static function getViewsForDate(int $propertyId, ?Carbon $date = null): int
    {
        $date = $date ?: Carbon::today();

        $record = static::where('property_id', $propertyId)
            ->where('view_date', $date->format('Y-m-d'))
            ->first();

        return $record ? $record->views_count : 0;
    }

    /**
     * Get views count for a property within a date range
     */
    public static function getViewsForDateRange(int $propertyId, Carbon $startDate, Carbon $endDate): int
    {
        return static::where('property_id', $propertyId)
            ->whereBetween('view_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->sum('views_count');
    }
}
