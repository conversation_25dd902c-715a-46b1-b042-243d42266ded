<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migration.
     */
    public function up(): void
    {
        Schema::create('re_property_daily_views', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('property_id')->constrained('re_properties')->onDelete('cascade');
            $table->date('view_date');
            $table->integer('views_count')->unsigned()->default(1);
            $table->timestamps();

            // Ensure one record per property per day
            $table->unique(['property_id', 'view_date']);
            
            // Index for faster queries
            $table->index(['property_id', 'view_date']);
            $table->index('view_date');
        });
    }

    /**
     * Reverse the migration.
     */
    public function down(): void
    {
        Schema::dropIfExists('re_property_daily_views');
    }
};
