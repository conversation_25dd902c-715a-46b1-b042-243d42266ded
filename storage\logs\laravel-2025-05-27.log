[2025-05-27 15:10:29] local.ERROR: Method Illuminate\Database\Eloquent\Collection::wherePublished does not exist. {"view":{"view":"D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\single-layouts\\partials\\agent.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1994525047 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#3281</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1994525047\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","property":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Xmetr\\RealEstate\\Models\\Property</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23746 title=\"9 occurrences\">#3746</a><samp data-depth=1 id=sf-dump-*********-ref23746 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">re_properties</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:36</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>96</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Modern and bright studio apartment 29m2 in Belgrano</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">rent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"207 characters\">Central hot water. LG inverter air conditioner, cold-heat. Electronic lock with code. Large window with double sealed glass DWH. Built-in kitchen with electric stove and oven. Refrigerator and storage space.</span>\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"651 characters\">&lt;p&gt;Central hot water. LG inverter air conditioner, cold-heat. Electronic lock with code. Large window with double sealed glass DWH. Built-in kitchen with electric stove and oven. Refrigerator and storage space. Fully equipped bathroom with shower, screen, mirror, vanity and exhaust fan. Building with SUM, coworking, gym, BBQ, pool and solarium. Laundry and bike storage. Great location. 3 blocks from Av. Cabildo, subway and bus. 2 blocks from Monroe Avenue, 2 blocks from Juramento Avenue and 1 block from Kramer Avenue. Requirements: 1 month in advance, 1 month deposit per year of contract. Contract for 12 or 24 months. Pets are not allowed!&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Vidal 2300, Belgrano, Capital Federal, &#1040;&#1088;&#1075;&#1077;&#1085;&#1090;&#1080;&#1085;&#1072;</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"536 characters\">{&quot;1&quot;:&quot;accounts\\/m-128\\/********11111.webp&quot;,&quot;2&quot;:&quot;accounts\\/m-130\\/****************.webp&quot;,&quot;3&quot;:&quot;accounts\\/m-129\\/********1111111.webp&quot;,&quot;4&quot;:&quot;accounts\\/m-131\\/2.webp&quot;,&quot;5&quot;:&quot;accounts\\/m-120\\/1.webp&quot;,&quot;6&quot;:&quot;accounts\\/m-127\\/********11.webp&quot;,&quot;7&quot;:&quot;accounts\\/m-132\\/22.webp&quot;,&quot;8&quot;:&quot;accounts\\/m-133\\/222.webp&quot;,&quot;9&quot;:&quot;accounts\\/m-134\\/2222.webp&quot;,&quot;10&quot;:&quot;accounts\\/m-121\\/11.webp&quot;,&quot;11&quot;:&quot;accounts\\/m-122\\/111.webp&quot;,&quot;12&quot;:&quot;accounts\\/m-123\\/1111.webp&quot;,&quot;13&quot;:&quot;accounts\\/m-124\\/11111.webp&quot;,&quot;14&quot;:&quot;accounts\\/m-125\\/1111111.webp&quot;,&quot;15&quot;:&quot;accounts\\/m-126\\/********.webp&quot;}</span>\"
    \"<span class=sf-dump-key>floor_plans</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&quot;[]&quot;</span>\"
    \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>square</span>\" => <span class=sf-dump-num>29.0</span>
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">450.00</span>\"
    \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8175</span>
    \"<span class=sf-dump-key>period</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\PropertyPeriodEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PropertyPeriodEnum</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23921 title=\"2 occurrences\">#3921</a><samp data-depth=3 id=sf-dump-*********-ref23921 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"5 characters\">month</span>\"
    </samp>}
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-num>15</span>
    \"<span class=sf-dump-key>author_type</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Account</span>\"
    \"<span class=sf-dump-key>moderation_status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>reject_reason</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2024-11-27</span>\"
    \"<span class=sf-dump-key>auto_renew</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>never_expired</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-28 21:27:30</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-12-21 23:50:08</span>\"
    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-34.5624645</span>\"
    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"18 characters\">-58.46199900000001</span>\"
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>93</span>
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>22</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>188</span>
    \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>private_notes</span>\" => \"\"
    \"<span class=sf-dump-key>price_formatted</span>\" => \"<span class=sf-dump-str title=\"6 characters\">$ 450 </span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:35</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>96</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Modern and bright studio apartment 29m2 in Belgrano</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">rent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"207 characters\">Central hot water. LG inverter air conditioner, cold-heat. Electronic lock with code. Large window with double sealed glass DWH. Built-in kitchen with electric stove and oven. Refrigerator and storage space.</span>\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"651 characters\">&lt;p&gt;Central hot water. LG inverter air conditioner, cold-heat. Electronic lock with code. Large window with double sealed glass DWH. Built-in kitchen with electric stove and oven. Refrigerator and storage space. Fully equipped bathroom with shower, screen, mirror, vanity and exhaust fan. Building with SUM, coworking, gym, BBQ, pool and solarium. Laundry and bike storage. Great location. 3 blocks from Av. Cabildo, subway and bus. 2 blocks from Monroe Avenue, 2 blocks from Juramento Avenue and 1 block from Kramer Avenue. Requirements: 1 month in advance, 1 month deposit per year of contract. Contract for 12 or 24 months. Pets are not allowed!&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Vidal 2300, Belgrano, Capital Federal, &#1040;&#1088;&#1075;&#1077;&#1085;&#1090;&#1080;&#1085;&#1072;</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"536 characters\">{&quot;1&quot;:&quot;accounts\\/m-128\\/********11111.webp&quot;,&quot;2&quot;:&quot;accounts\\/m-130\\/****************.webp&quot;,&quot;3&quot;:&quot;accounts\\/m-129\\/********1111111.webp&quot;,&quot;4&quot;:&quot;accounts\\/m-131\\/2.webp&quot;,&quot;5&quot;:&quot;accounts\\/m-120\\/1.webp&quot;,&quot;6&quot;:&quot;accounts\\/m-127\\/********11.webp&quot;,&quot;7&quot;:&quot;accounts\\/m-132\\/22.webp&quot;,&quot;8&quot;:&quot;accounts\\/m-133\\/222.webp&quot;,&quot;9&quot;:&quot;accounts\\/m-134\\/2222.webp&quot;,&quot;10&quot;:&quot;accounts\\/m-121\\/11.webp&quot;,&quot;11&quot;:&quot;accounts\\/m-122\\/111.webp&quot;,&quot;12&quot;:&quot;accounts\\/m-123\\/1111.webp&quot;,&quot;13&quot;:&quot;accounts\\/m-124\\/11111.webp&quot;,&quot;14&quot;:&quot;accounts\\/m-125\\/1111111.webp&quot;,&quot;15&quot;:&quot;accounts\\/m-126\\/********.webp&quot;}</span>\"
    \"<span class=sf-dump-key>floor_plans</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&quot;[]&quot;</span>\"
    \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>square</span>\" => <span class=sf-dump-num>29.0</span>
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">450.00</span>\"
    \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8175</span>
    \"<span class=sf-dump-key>period</span>\" => \"<span class=sf-dump-str title=\"5 characters\">month</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-num>15</span>
    \"<span class=sf-dump-key>author_type</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Account</span>\"
    \"<span class=sf-dump-key>moderation_status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>reject_reason</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2024-11-27</span>\"
    \"<span class=sf-dump-key>auto_renew</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>never_expired</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-28 21:27:30</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-12-21 23:50:08</span>\"
    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-34.5624645</span>\"
    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"18 characters\">-58.46199900000001</span>\"
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>93</span>
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>22</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>188</span>
    \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>private_notes</span>\" => \"\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:17</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Xmetr\\RealEstate\\Enums\\PropertyStatusEnum</span>\"
    \"<span class=sf-dump-key>moderation_status</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Xmetr\\RealEstate\\Enums\\ModerationStatusEnum</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Xmetr\\RealEstate\\Enums\\PropertyTypeEnum</span>\"
    \"<span class=sf-dump-key>period</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Xmetr\\RealEstate\\Enums\\PropertyPeriodEnum</span>\"
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>private_notes</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>number_bedroom</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>number_bathroom</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>number_floor</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>floor_plans</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>period</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\PropertyPeriodEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PropertyPeriodEnum</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23921 title=\"2 occurrences\">#3921</a>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>slugable</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Slug\\Models\\Slug
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Slug\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Slug</span></span> {<a class=sf-dump-ref>#3751</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>metadata</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3769</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>currency</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Currency
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Currency</span></span> {<a class=sf-dump-ref>#3782</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">re_currencies</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>city</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Location\\Models\\City
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Location\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">City</span></span> {<a class=sf-dump-ref>#3836</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">cities</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:9</span> [ &#8230;9]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>country</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Location\\Models\\Country
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Location\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Country</span></span> {<a class=sf-dump-ref>#3805</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">countries</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3779</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>author</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Account
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Account</span></span> {<a class=sf-dump-ref>#3895</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_accounts</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:24</span> [ &#8230;24]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:24</span> [ &#8230;24]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:13</span> [ &#8230;13]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:16</span> [ &#8230;16]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
    </samp>}
    \"<span class=sf-dump-key>features</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3946</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>facilities</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3968</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:29</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">content</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"10 characters\">project_id</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"14 characters\">number_bedroom</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"15 characters\">number_bathroom</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"12 characters\">number_floor</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"6 characters\">square</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"11 characters\">currency_id</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"7 characters\">city_id</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"10 characters\">country_id</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"6 characters\">period</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"9 characters\">author_id</span>\"
    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"11 characters\">author_type</span>\"
    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"11 characters\">expire_date</span>\"
    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"10 characters\">auto_renew</span>\"
    <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"8 characters\">latitude</span>\"
    <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"9 characters\">longitude</span>\"
    <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"9 characters\">unique_id</span>\"
    <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">private_notes</span>\"
    <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">floor_plans</span>\"
    <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"13 characters\">reject_reason</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","images":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">https://xmetr.gc/storage/accounts/m-128/********11111.webp</span>\"
  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"61 characters\">https://xmetr.gc/storage/accounts/m-130/****************.webp</span>\"
  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"60 characters\">https://xmetr.gc/storage/accounts/m-129/********1111111.webp</span>\"
  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"46 characters\">https://xmetr.gc/storage/accounts/m-131/2.webp</span>\"
  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"46 characters\">https://xmetr.gc/storage/accounts/m-120/1.webp</span>\"
  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"55 characters\">https://xmetr.gc/storage/accounts/m-127/********11.webp</span>\"
  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"47 characters\">https://xmetr.gc/storage/accounts/m-132/22.webp</span>\"
  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"48 characters\">https://xmetr.gc/storage/accounts/m-133/222.webp</span>\"
  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"49 characters\">https://xmetr.gc/storage/accounts/m-134/2222.webp</span>\"
  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"47 characters\">https://xmetr.gc/storage/accounts/m-121/11.webp</span>\"
  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"48 characters\">https://xmetr.gc/storage/accounts/m-122/111.webp</span>\"
  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"49 characters\">https://xmetr.gc/storage/accounts/m-123/1111.webp</span>\"
  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"50 characters\">https://xmetr.gc/storage/accounts/m-124/11111.webp</span>\"
  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"52 characters\">https://xmetr.gc/storage/accounts/m-125/1111111.webp</span>\"
  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"53 characters\">https://xmetr.gc/storage/accounts/m-126/********.webp</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","style":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">\"<span class=sf-dump-str>5</span>\"
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","data":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Modern and bright studio apartment 29m2 in Belgrano</span>\"
  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">$ 450 </span>\"
  \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Buenos Aires</span>\"
  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Argentina</span>\"
  \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Rent Apartment</span>\"
  \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-num>1</span>
  \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-num>1</span>
  \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"5 characters\">29 &#1084;&#178;</span>\"
  \"<span class=sf-dump-key>deposit</span>\" => \"\"
  \"<span class=sf-dump-key>commission</span>\" => \"\"
  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"207 characters\">Central hot water. LG inverter air conditioner, cold-heat. Electronic lock with code. Large window with double sealed glass DWH. Built-in kitchen with electric stove and oven. Refrigerator and storage space.</span>\"
  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"58 characters\">https://xmetr.gc/storage/accounts/m-128/********11111.webp</span>\"
  \"<span class=sf-dump-key>property_url</span>\" => \"<span class=sf-dump-str title=\"82 characters\">https://xmetr.gc/en/properties/modern-and-bright-studio-apartment-29m2-in-belgrano</span>\"
  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">https://xmetr.gc/storage/accounts/m-128/********11111.webp</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"61 characters\">https://xmetr.gc/storage/accounts/m-130/****************.webp</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"60 characters\">https://xmetr.gc/storage/accounts/m-129/********1111111.webp</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"46 characters\">https://xmetr.gc/storage/accounts/m-131/2.webp</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"46 characters\">https://xmetr.gc/storage/accounts/m-120/1.webp</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"55 characters\">https://xmetr.gc/storage/accounts/m-127/********11.webp</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"47 characters\">https://xmetr.gc/storage/accounts/m-132/22.webp</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"48 characters\">https://xmetr.gc/storage/accounts/m-133/222.webp</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"49 characters\">https://xmetr.gc/storage/accounts/m-134/2222.webp</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"47 characters\">https://xmetr.gc/storage/accounts/m-121/11.webp</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"48 characters\">https://xmetr.gc/storage/accounts/m-122/111.webp</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"49 characters\">https://xmetr.gc/storage/accounts/m-123/1111.webp</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"50 characters\">https://xmetr.gc/storage/accounts/m-124/11111.webp</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"52 characters\">https://xmetr.gc/storage/accounts/m-125/1111111.webp</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"53 characters\">https://xmetr.gc/storage/accounts/m-126/********.webp</span>\"
  </samp>]
  \"<span class=sf-dump-key>agent</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Rina F</span>\"
  \"<span class=sf-dump-key>agent_phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
  \"<span class=sf-dump-key>agent_email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>agent_whatsapp</span>\" => \"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
  \"<span class=sf-dump-key>agent_telegram</span>\" => \"<span class=sf-dump-str title=\"5 characters\">@rina</span>\"
  \"<span class=sf-dump-key>agent_url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">https://xmetr.gc/en/agents/rina</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","deposit":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","image":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"28 characters\">accounts/m-126/********.webp</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","account":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Xmetr\\RealEstate\\Models\\Account</span> {<a class=sf-dump-ref>#3895</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_accounts</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:24</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>15</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rina</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str>F</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"2 characters\">&#1052;.</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$mPh0Jh3iv75UdQt8DSfIc.Q483pgIY4lByIfF1zG8NgYOriPrgZaO</span>\"
    \"<span class=sf-dump-key>avatar_id</span>\" => <span class=sf-dump-num>178</span>
    \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-04-28</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
    \"<span class=sf-dump-key>credits</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-11 20:01:55</span>\"
    \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">GngDsKoQ8r7X8uiYpbcHkW1Ws1WjMsBAJUz8NwWUJo6lTsIUgsKiWK1ddqaT</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-11 22:01:55</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-28 17:19:24</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_public_profile</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>company</span>\" => \"\"
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>28</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>approved_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:24</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>15</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Rina</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str>F</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"2 characters\">&#1052;.</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$mPh0Jh3iv75UdQt8DSfIc.Q483pgIY4lByIfF1zG8NgYOriPrgZaO</span>\"
    \"<span class=sf-dump-key>avatar_id</span>\" => <span class=sf-dump-num>178</span>
    \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-04-28</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
    \"<span class=sf-dump-key>credits</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-11 20:01:55</span>\"
    \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">GngDsKoQ8r7X8uiYpbcHkW1Ws1WjMsBAJUz8NwWUJo6lTsIUgsKiWK1ddqaT</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-11 22:01:55</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-04-28 17:19:24</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_public_profile</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>company</span>\" => \"\"
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>28</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>approved_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>package_start_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>package_end_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>is_public_profile</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>company</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">hashed</span>\"
    \"<span class=sf-dump-key>approved_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>metadata</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3807</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>slugable</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Slug\\Models\\Slug
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Slug\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Slug</span></span> {<a class=sf-dump-ref>#3918</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>avatar</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Media\\Models\\MediaFile
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Media\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MediaFile</span></span> {<a class=sf-dump-ref>#4010</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">media_files</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:13</span> [ &#8230;13]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:13</span> [ &#8230;13]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>properties</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4006</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:18</span> [ &#8230;18]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">first_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">last_name</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">username</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">avatar_id</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"3 characters\">dob</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">gender</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">company</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"10 characters\">country_id</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"7 characters\">city_id</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"17 characters\">is_public_profile</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","whatsapp":"<pre class=sf-dump id=sf-dump-1616253937 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">***********</span>\"
</pre><script>Sfdump(\"sf-dump-1616253937\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","telegram":"<pre class=sf-dump id=sf-dump-1570103820 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"5 characters\">@rina</span>\"
</pre><script>Sfdump(\"sf-dump-1570103820\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","commission":"<pre class=sf-dump id=sf-dump-710022075 data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-710022075\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Method Illuminate\\Database\\Eloquent\\Collection::wherePublished does not exist. at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:112)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\single-layouts\\partials\\agent.blade.php(91): Illuminate\\Support\\Collection->__call('wherePublished', Array)
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#8 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\single-layouts\\style-5.blade.php(61): Illuminate\\View\\View->render()
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#16 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\property.blade.php(52): Illuminate\\View\\View->render()
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#23 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#24 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(682): Illuminate\\View\\View->render()
#25 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(661): Xmetr\\Theme\\Theme->setUpContent('theme.xmetr::vi...', Array)
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->scope('real-estate.pro...', Array, 'plugins/real-es...')
#27 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#28 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(116): Xmetr\\Theme\\Http\\Controllers\\PublicController->getView('modern-and-brig...', 'properties')
#29 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('properties', 'modern-and-brig...')
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#31 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#34 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#94 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#95 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#96 {main}

[previous exception] [object] (BadMethodCallException(code: 0): Method Illuminate\\Database\\Eloquent\\Collection::wherePublished does not exist. at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:112)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\572a4246958a8a6e4e69b9df9e46df8a.php(24): Illuminate\\Support\\Collection->__call('wherePublished', Array)
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#8 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\ad88605931b659db0874ab390e4ac9a6.php(62): Illuminate\\View\\View->render()
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#16 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\03b54f071e37eb41b5e8510b59b8e693.php(53): Illuminate\\View\\View->render()
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#23 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#24 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(682): Illuminate\\View\\View->render()
#25 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(661): Xmetr\\Theme\\Theme->setUpContent('theme.xmetr::vi...', Array)
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->scope('real-estate.pro...', Array, 'plugins/real-es...')
#27 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#28 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(116): Xmetr\\Theme\\Http\\Controllers\\PublicController->getView('modern-and-brig...', 'properties')
#29 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('properties', 'modern-and-brig...')
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#31 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#34 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#94 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#95 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#96 {main}
"} 
