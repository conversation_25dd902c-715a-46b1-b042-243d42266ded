<?php

namespace Xmetr\RealEstate\Models;

use Xmetr\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class PropertyContactClick extends BaseModel
{
    protected $table = 're_property_contact_clicks';

    protected $fillable = [
        'property_id',
        'contact_type',
        'click_date',
        'clicks_count',
    ];

    protected $casts = [
        'click_date' => 'date',
        'clicks_count' => 'integer',
    ];

    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class, 'property_id');
    }

    /**
     * Increment clicks for a property contact type on a specific date
     */
    public static function incrementClicks(int $propertyId, string $contactType, ?Carbon $date = null): void
    {
        $date = $date ?: Carbon::today();

        $record = static::firstOrCreate(
            [
                'property_id' => $propertyId,
                'contact_type' => $contactType,
                'click_date' => $date->format('Y-m-d'),
            ],
            [
                'clicks_count' => 0,
            ]
        );

        $record->increment('clicks_count');
    }

    /**
     * Get clicks count for a property contact type on a specific date
     */
    public static function getClicksForDate(int $propertyId, string $contactType, ?Carbon $date = null): int
    {
        $date = $date ?: Carbon::today();

        $record = static::where('property_id', $propertyId)
            ->where('contact_type', $contactType)
            ->where('click_date', $date->format('Y-m-d'))
            ->first();

        return $record ? $record->clicks_count : 0;
    }

    /**
     * Get total clicks count for a property contact type
     */
    public static function getTotalClicks(int $propertyId, string $contactType): int
    {
        return static::where('property_id', $propertyId)
            ->where('contact_type', $contactType)
            ->sum('clicks_count');
    }

    /**
     * Get clicks count for a property contact type within a date range
     */
    public static function getClicksForDateRange(int $propertyId, string $contactType, Carbon $startDate, Carbon $endDate): int
    {
        return static::where('property_id', $propertyId)
            ->where('contact_type', $contactType)
            ->whereBetween('click_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->sum('clicks_count');
    }
}
