<?php

namespace Xmetr\RealEstate\Commands;

use Xmetr\RealEstate\Models\PropertyDailyView;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CleanupDailyViewsCommand extends Command
{
    protected $signature = 'real-estate:cleanup-daily-views {--days=90 : Number of days to keep daily view records}';

    protected $description = 'Clean up old daily view records to prevent database bloat';

    public function handle(): int
    {
        $daysToKeep = (int) $this->option('days');
        
        if ($daysToKeep < 1) {
            $this->error('Days must be a positive number');
            return self::FAILURE;
        }

        $cutoffDate = Carbon::now()->subDays($daysToKeep);
        
        $this->info("Cleaning up daily view records older than {$daysToKeep} days (before {$cutoffDate->format('Y-m-d')})...");
        
        $deletedCount = PropertyDailyView::where('view_date', '<', $cutoffDate->format('Y-m-d'))->delete();
        
        $this->info("Deleted {$deletedCount} old daily view records.");
        
        return self::SUCCESS;
    }
}
