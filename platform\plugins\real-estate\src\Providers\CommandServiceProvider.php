<?php

namespace Xmetr\RealEstate\Providers;

use Xmetr\RealEstate\Commands\CleanupDailyViewsCommand;
use Xmetr\RealEstate\Commands\RenewPropertiesCommand;
use Illuminate\Support\ServiceProvider;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->commands([
            RenewPropertiesCommand::class,
            CleanupDailyViewsCommand::class,
        ]);
    }
}
