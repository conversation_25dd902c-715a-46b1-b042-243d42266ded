<?php

namespace Xmetr\RealEstate\Tables;

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Payment\Models\Payment;
use Xmetr\RealEstate\Enums\ModerationStatusEnum;
use Xmetr\RealEstate\Enums\PropertyStatusEnum;
use Xmetr\RealEstate\Models\Project;
use Xmetr\RealEstate\Models\Property;
use Xmetr\Table\Abstracts\TableAbstract;
use Xmetr\Table\Actions\DeleteAction;
use Xmetr\Table\Actions\EditAction;
use Xmetr\Table\BulkActions\DeleteBulkAction;
use Xmetr\Table\BulkChanges\CreatedAtBulkChange;
use Xmetr\Table\BulkChanges\NameBulkChange;
use Xmetr\Table\BulkChanges\SelectBulkChange;
use Xmetr\Table\BulkChanges\StatusBulkChange;
use Xmetr\Table\Columns\Column;
use Xmetr\Table\Columns\CreatedAtColumn;
use Xmetr\Table\Columns\EnumColumn;
use Xmetr\Table\Columns\IdColumn;
use Xmetr\Table\Columns\ImageColumn;
use Xmetr\Table\Columns\NameColumn;
use Xmetr\Table\Columns\StatusColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\Relations\Relation as EloquentRelation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class PropertyTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Property::class)
            ->addActions([
                EditAction::make()->route('property.edit'),
                DeleteAction::make()->route('property.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('views', function (Property $item) {
                return number_format($item->views);
            })
            ->editColumn('unique_id', function (Property $item) {
                return BaseHelper::clean($item->unique_id ?: '&mdash;');
            })
            ->filter(function ($query) {
                if ($keyword = $this->request->input('search.value')) {
                    $keyword = '%' . $keyword . '%';

                    return $query
                        ->where('name', 'LIKE', $keyword)
                        ->orWhere('unique_id', 'LIKE', $keyword)
                        ->orWhere('location', 'LIKE', $keyword)
                        ->orWhereHas('city', function ($query) use ($keyword) {
                            $query->where('name', 'LIKE', $keyword);
                        })
                        ->orWhereHas('state', function ($query) use ($keyword) {
                            $query->where('name', 'LIKE', $keyword);
                        })
                        ->orWhereHas('country', function ($query) use ($keyword) {
                            $query->where('name', 'LIKE', $keyword);
                        });
                }

                return $query;
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'images',
                'views',
                'whatsapp_clicks',
                'telegram_clicks',
                'phone_clicks',
                'status',
                'moderation_status',
                'created_at',
                'unique_id',
                'location',
            ]);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            ImageColumn::make()
                ->searchable(false)
                ->orderable(false),
            NameColumn::make()->route('property.edit'),
            Column::make('views')
                ->title(trans('plugins/real-estate::property.views')),
            Column::make('whatsapp_clicks')
                ->title(__('WhatsApp Clicks'))
                ->width(120),
            Column::make('telegram_clicks')
                ->title(__('Telegram Clicks'))
                ->width(120),
            Column::make('phone_clicks')
                ->title(__('Phone Clicks'))
                ->width(120),
            Column::make('unique_id')
                ->title(trans('plugins/real-estate::property.unique_id')),
            CreatedAtColumn::make(),
            StatusColumn::make(),
            EnumColumn::make('moderation_status')
                ->title(trans('plugins/real-estate::property.moderation_status'))
                ->width(150),
        ];
    }

    public function buttons(): array
    {
        $buttons = $this->addCreateButton(route('property.create'), 'property.create');

        if ($this->hasPermission('import-properties.index')) {
            $buttons['import'] = [
                'link' => route('properties.import.index'),
                'text' =>
                    BaseHelper::renderIcon('ti ti-upload')
                    . trans('plugins/real-estate::property.import_properties'),
            ];
        }

        if ($this->hasPermission('export-properties.index')) {
            $buttons['export'] = [
                'link' => route('export-properties.index'),
                'text' =>
                    BaseHelper::renderIcon('ti ti-download')
                    . trans('plugins/real-estate::property.export_properties'),
            ];
        }

        return $buttons;
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('property.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            NameBulkChange::make(),
            StatusBulkChange::make()->choices(PropertyStatusEnum::labels()),
            StatusBulkChange::make()
                ->name('moderation_status')
                ->title(trans('plugins/real-estate::property.moderation_status'))
                ->choices(ModerationStatusEnum::labels()),
            SelectBulkChange::make()
                ->name('project_id')
                ->title(trans('plugins/real-estate::property.form.project'))
                ->searchable()
                ->choices(fn () => Project::query()->pluck('name', 'id')->all()),
            CreatedAtBulkChange::make(),
        ];
    }

    public function applyFilterCondition(EloquentBuilder|QueryBuilder|EloquentRelation $query, string $key, string $operator, ?string $value): EloquentRelation|EloquentBuilder|QueryBuilder
    {
        if ($key == 'status') {
            switch ($value) {
                case 'expired':
                    // @phpstan-ignore-next-line
                    return $query->expired();
                case 'active':
                    // @phpstan-ignore-next-line
                    return $query->active();
            }
        }

        return parent::applyFilterCondition($query, $key, $operator, $value);
    }

    public function saveBulkChangeItem(Model|Payment $item, string $inputKey, ?string $inputValue): Model|bool
    {
        if ($inputKey === 'moderation_status') {
            $item->moderation_status = $inputValue;

            $item->save();
        }

        return parent::saveBulkChangeItem($item, $inputKey, $inputValue);
    }
}
